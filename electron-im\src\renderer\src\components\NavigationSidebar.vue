<template>
  <div class="flex flex-col w-20 bg-[#e4e7ed] shadow-lg">
    <!-- 导航按钮列表 -->
    <nav class="flex flex-col items-center py-4 space-y-3">
      <button
        v-for="item in navItems"
        :key="item.id"
        @click="setActiveItem(item.id)"
        :class="[
          'relative flex flex-col items-center justify-center w-16 h-16 rounded-xl transition-all duration-200',
          activeItem === item.id
            ? 'bg-white shadow-md text-gray-700'
            : 'text-gray-600 hover:bg-[#d8dae0] hover:text-gray-700'
        ]"
      >
        <img :src="item.icon" :alt="item.label" class="w-5 h-5 mb-1" />
        <span class="text-xs font-medium">{{ item.label }}</span>
      </button>
    </nav>

    <!-- 底部用户头像 -->
    <div class="mt-auto mb-4 flex justify-center relative">
      <button
        @click="toggleUserPopover"
        class="w-10 h-10 rounded-lg flex items-center justify-center text-white font-medium text-sm transition-all duration-200 hover:opacity-80"
        :style="{ backgroundColor: userAvatarColor }"
      >
        {{ userAvatar }}
      </button>

      <!-- 用户弹出菜单 -->
      <div
        v-if="showUserPopover"
        class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[120px] z-50"
      >
        <div class="px-3 py-2 border-b border-gray-100">
          <div class="text-sm font-medium text-gray-900">{{ userDisplayName }}</div>
          <div class="text-xs text-gray-500">{{ userEmail }}</div>
        </div>
        <button
          @click="handleLogout"
          class="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors"
        >
          退出登录
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { generateAvatarColor } from '../utils/avatarColors'

// 用户状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

// 弹出菜单状态
const showUserPopover = ref(false)

// 计算用户头像显示文字
const userAvatar = computed(() => {
  const user = userStore.currentUser.value
  if (user?.displayName) {
    // 获取姓名的后两个字作为头像
    return user.displayName.length >= 2 ? user.displayName.slice(-2) : user.displayName
  }
  return '用户'
})

// 计算用户头像颜色
const userAvatarColor = computed(() => {
  const user = userStore.currentUser.value
  if (user?.displayName) {
    return generateAvatarColor(user.displayName)
  }
  return '#3b82f6' // 默认蓝色
})

// 计算用户显示名称
const userDisplayName = computed(() => {
  return userStore.userDisplayName.value || '用户'
})

// 计算用户邮箱
const userEmail = computed(() => {
  return userStore.currentUser.value?.email || ''
})

// 导航项配置
const navItems = [
  {
    id: 'search',
    label: '搜索',
    icon: new URL('../assets/navigation-icons/search.png', import.meta.url).href
  },
  {
    id: 'documents',
    label: '文档',
    icon: new URL('../assets/navigation-icons/document.png', import.meta.url).href
  },
  {
    id: 'calendar',
    label: '日历',
    icon: new URL('../assets/navigation-icons/calendar.png', import.meta.url).href
  },
  {
    id: 'meetings',
    label: '会议',
    icon: new URL('../assets/navigation-icons/meeting.png', import.meta.url).href
  },
  {
    id: 'mail',
    label: '邮箱',
    icon: new URL('../assets/navigation-icons/mailbox.png', import.meta.url).href
  },
  {
    id: 'workspace',
    label: '工作台',
    icon: new URL('../assets/navigation-icons/workbench.png', import.meta.url).href
  },
  {
    id: 'assistant',
    label: '助手',
    icon: new URL('../assets/navigation-icons/assistant.png', import.meta.url).href
  },
  {
    id: 'profile',
    label: '我的',
    icon: new URL('../assets/navigation-icons/profile.png', import.meta.url).href
  }
]

// 当前激活的导航项
const activeItem = ref('search')

// 设置激活项
const setActiveItem = (itemId: string) => {
  activeItem.value = itemId
  // 发出事件给父组件
  emit('nav-change', itemId)
}

// 切换用户弹出菜单
const toggleUserPopover = () => {
  showUserPopover.value = !showUserPopover.value
}

// 处理退出登录
const handleLogout = () => {
  userStore.logout()
  messageStore.disconnectWebSocket()
  showUserPopover.value = false
  // 触发页面重新加载，显示登录页面
  window.location.reload()
}

// 点击外部关闭弹出菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showUserPopover.value = false
  }
}

// 组件挂载时添加事件监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 定义事件
const emit = defineEmits<{
  'nav-change': [itemId: string]
}>()
</script>
