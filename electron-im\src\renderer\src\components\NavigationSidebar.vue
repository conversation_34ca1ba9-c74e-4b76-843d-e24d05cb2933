<template>
  <div class="flex flex-col w-16 bg-blue-600 shadow-lg">
    <!-- 导航按钮列表 -->
    <nav class="flex flex-col items-center py-4 space-y-2">
      <button
        v-for="item in navItems"
        :key="item.id"
        @click="setActiveItem(item.id)"
        :class="[
          'relative flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-200 group',
          activeItem === item.id
            ? 'bg-white text-blue-600 shadow-md'
            : 'text-blue-100 hover:bg-blue-500 hover:text-white'
        ]"
        :title="item.label"
      >
        <img :src="item.icon" :alt="item.label" class="w-6 h-6" />

        <!-- 激活状态指示器 -->
        <div
          v-if="activeItem === item.id"
          class="absolute -right-1 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-blue-600 rounded-l-full"
        />

        <!-- 悬停提示 -->
        <div
          class="absolute left-full ml-3 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10"
        >
          {{ item.label }}
        </div>
      </button>
    </nav>

    <!-- 底部用户头像 -->
    <div class="mt-auto mb-4 flex justify-center">
      <button
        @click="setActiveItem('profile')"
        :class="[
          'flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200 group',
          activeItem === 'profile'
            ? 'bg-white text-blue-600 shadow-md'
            : 'bg-blue-500 text-white hover:bg-blue-400'
        ]"
        title="我的"
      >
        <img src="../assets/navigation-icons/profile.png" alt="我的" class="w-6 h-6" />

        <!-- 悬停提示 -->
        <div
          class="absolute left-full ml-3 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10"
        >
          我的
        </div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 导航项配置
const navItems = [
  {
    id: 'search',
    label: '搜索',
    icon: new URL('../assets/navigation-icons/search.png', import.meta.url).href
  },
  {
    id: 'documents',
    label: '文档',
    icon: new URL('../assets/navigation-icons/document.png', import.meta.url).href
  },
  {
    id: 'calendar',
    label: '日历',
    icon: new URL('../assets/navigation-icons/calendar.png', import.meta.url).href
  },
  {
    id: 'meetings',
    label: '会议',
    icon: new URL('../assets/navigation-icons/meeting.png', import.meta.url).href
  },
  {
    id: 'mail',
    label: '邮箱',
    icon: new URL('../assets/navigation-icons/mailbox.png', import.meta.url).href
  },
  {
    id: 'workspace',
    label: '工作台',
    icon: new URL('../assets/navigation-icons/workbench.png', import.meta.url).href
  },
  {
    id: 'assistant',
    label: '助手',
    icon: new URL('../assets/navigation-icons/assistant.png', import.meta.url).href
  }
]

// 当前激活的导航项
const activeItem = ref('search')

// 设置激活项
const setActiveItem = (itemId: string) => {
  activeItem.value = itemId
  // 发出事件给父组件
  emit('nav-change', itemId)
}

// 定义事件
const emit = defineEmits<{
  'nav-change': [itemId: string]
}>()
</script>
