<template>
  <div class="flex flex-col w-20 bg-[#e4e7ed] shadow-lg">
    <!-- 导航按钮列表 -->
    <nav class="flex flex-col items-center py-4 space-y-3">
      <button
        v-for="item in navItems"
        :key="item.id"
        @click="setActiveItem(item.id)"
        :class="[
          'relative flex flex-col items-center justify-center w-16 h-16 rounded-xl transition-all duration-200',
          activeItem === item.id
            ? 'bg-white shadow-md text-gray-700'
            : 'text-gray-600 hover:bg-[#d8dae0] hover:text-gray-700'
        ]"
      >
        <img :src="item.icon" :alt="item.label" class="w-5 h-5 mb-1" />
        <span class="text-xs font-medium">{{ item.label }}</span>
      </button>
    </nav>

    <!-- 底部用户头像 -->
    <div class="mt-auto mb-4 flex justify-center">
      <button
        @click="setActiveItem('profile')"
        :class="[
          'flex flex-col items-center justify-center w-16 h-16 rounded-xl transition-all duration-200',
          activeItem === 'profile'
            ? 'bg-white shadow-md text-gray-700'
            : 'text-gray-600 hover:bg-[#d8dae0] hover:text-gray-700'
        ]"
      >
        <img src="../assets/navigation-icons/profile.png" alt="我的" class="w-5 h-5 mb-1" />
        <span class="text-xs font-medium">我的</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 导航项配置
const navItems = [
  {
    id: 'search',
    label: '搜索',
    icon: new URL('../assets/navigation-icons/search.png', import.meta.url).href
  },
  {
    id: 'documents',
    label: '文档',
    icon: new URL('../assets/navigation-icons/document.png', import.meta.url).href
  },
  {
    id: 'calendar',
    label: '日历',
    icon: new URL('../assets/navigation-icons/calendar.png', import.meta.url).href
  },
  {
    id: 'meetings',
    label: '会议',
    icon: new URL('../assets/navigation-icons/meeting.png', import.meta.url).href
  },
  {
    id: 'mail',
    label: '邮箱',
    icon: new URL('../assets/navigation-icons/mailbox.png', import.meta.url).href
  },
  {
    id: 'workspace',
    label: '工作台',
    icon: new URL('../assets/navigation-icons/workbench.png', import.meta.url).href
  },
  {
    id: 'assistant',
    label: '助手',
    icon: new URL('../assets/navigation-icons/assistant.png', import.meta.url).href
  },
]

// 当前激活的导航项
const activeItem = ref('search')

// 设置激活项
const setActiveItem = (itemId: string) => {
  activeItem.value = itemId
  // 发出事件给父组件
  emit('nav-change', itemId)
}

// 定义事件
const emit = defineEmits<{
  'nav-change': [itemId: string]
}>()
</script>
